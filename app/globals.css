@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%; /* #FFFFFF */
  --foreground: 0 0% 8.6%; /* #161616 */
  --card: 0 0% 100%; /* #FFFFFF */
  --card-foreground: 0 0% 8.6%; /* #161616 */
  --popover: 0 0% 100%; /* #FFFFFF */
  --popover-foreground: 0 0% 8.6%; /* #161616 */
  --primary: 134 29% 29%; /* #39744a */
  --primary-foreground: 0 0% 100%; /* #FFFFFF */
  --secondary: 89 25% 52%; /* #81a66a */
  --secondary-foreground: 0 0% 8.6%; /* #161616 */
  --muted: 0 0% 96%; /* #F5F5F5 */
  --muted-foreground: 0 0% 71.4%; /* #b6b6b6 */
  --accent: 89 25% 52%; /* #81a66a */
  --accent-foreground: 0 0% 8.6%; /* #161616 */
  --destructive: 0 84.2% 60.2%; /* #EF4444 */
  --destructive-foreground: 0 0% 100%; /* #FFFFFF */
  --border: 0 0% 89.8%; /* #E5E5E5 */
  --input: 0 0% 89.8%; /* #E5E5E5 */
  --ring: 134 29% 29%; /* #39744a */
  --radius: 0.5rem;

  /* Custom Fleet Colors */
  --fleet-grey: 0 0% 58.4%; /* #959393 */
  --fleet-light-mud: 48 100% 87.1%; /* #fff2bd */
  --fleet-mud: 45 56% 57.1%; /* #cead57 */
  --fleet-brown: 45 85% 19.6%; /* #654a08 */

  /* Status Colors */
  --status-pending: 45 93% 88%; /* #fef3c7 - light yellow background */
  --status-pending-foreground: 45 93% 20%; /* #92400e - dark yellow text */
  --status-bidding: 217 91% 88%; /* #dbeafe - light blue background */
  --status-bidding-foreground: 217 91% 20%; /* #1e40af - dark blue text */
  --status-matched: 134 29% 88%; /* #d1fae5 - light green background */
  --status-matched-foreground: 134 29% 20%; /* #065f46 - dark green text */
  --status-completed: 142 76% 88%; /* #dcfce7 - light green background */
  --status-completed-foreground: 142 76% 20%; /* #166534 - dark green text */
  --status-cancelled: 0 84% 88%; /* #fee2e2 - light red background */
  --status-cancelled-foreground: 0 84% 20%; /* #991b1b - dark red text */
}



@media (prefers-color-scheme: dark) {
  :root {
    --background: 0 0% 8.6%; /* #161616 */
    --foreground: 0 0% 100%; /* #FFFFFF */
    --card: 0 0% 12%; /* #1F1F1F */
    --card-foreground: 0 0% 100%; /* #FFFFFF */
    --popover: 0 0% 12%; /* #1F1F1F */
    --popover-foreground: 0 0% 100%; /* #FFFFFF */
    --primary: 134 29% 35%; /* #4a8a5a - lighter version for dark mode */
    --primary-foreground: 0 0% 100%; /* #FFFFFF */
    --secondary: 89 25% 45%; /* #6d9157 - darker version for dark mode */
    --secondary-foreground: 0 0% 100%; /* #FFFFFF */
    --muted: 0 0% 15%; /* #262626 */
    --muted-foreground: 0 0% 58.4%; /* #959393 */
    --accent: 89 25% 45%; /* #6d9157 */
    --accent-foreground: 0 0% 100%; /* #FFFFFF */
    --destructive: 0 62.8% 50%; /* #DC2626 */
    --destructive-foreground: 0 0% 100%; /* #FFFFFF */
    --border: 0 0% 20%; /* #333333 */
    --input: 0 0% 20%; /* #333333 */
    --ring: 134 29% 35%; /* #4a8a5a */

    /* Custom Fleet Colors for Dark Mode */
    --fleet-grey: 0 0% 65%; /* #A6A6A6 - lighter for dark mode */
    --fleet-light-mud: 48 80% 75%; /* #f0e09a - darker for dark mode */
    --fleet-mud: 45 50% 50%; /* #b8984a - adjusted for dark mode */
    --fleet-brown: 45 70% 30%; /* #8a6b0f - lighter for dark mode */

    /* Status Colors for Dark Mode */
    --status-pending: 45 93% 20%; /* #92400e - dark yellow background */
    --status-pending-foreground: 45 93% 88%; /* #fef3c7 - light yellow text */
    --status-bidding: 217 91% 20%; /* #1e40af - dark blue background */
    --status-bidding-foreground: 217 91% 88%; /* #dbeafe - light blue text */
    --status-matched: 134 29% 20%; /* #065f46 - dark green background */
    --status-matched-foreground: 134 29% 88%; /* #d1fae5 - light green text */
    --status-completed: 142 76% 20%; /* #166534 - dark green background */
    --status-completed-foreground: 142 76% 88%; /* #dcfce7 - light green text */
    --status-cancelled: 0 84% 20%; /* #991b1b - dark red background */
    --status-cancelled-foreground: 0 84% 88%; /* #fee2e2 - light red text */
  }
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

* {
  border-color: hsl(var(--border));
}
